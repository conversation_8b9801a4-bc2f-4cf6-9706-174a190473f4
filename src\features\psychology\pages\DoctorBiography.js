import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { FaArrowLeft, FaGraduationCap, FaBriefcase, FaMapMarkerAlt } from 'react-icons/fa';

const DoctorBiography = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { doctor } = location.state || {};

  // Default doctor data if none provided
  const defaultDoctor = {
    id: 1,
    name: 'Dr. <PERSON><PERSON><PERSON>',
    specialization: 'Pengalaman 4 tahun',
    image: 'https://images.pexels.com/photos/5327921/pexels-photo-5327921.jpeg?auto=compress&cs=tinysrgb&w=400',
    bio: 'Kami siap mendampingi perjalanan Anda menuju kesehatan emosional. Dapatkan bimbingan dari psikolog profesional dan temukan solusi terbaik untuk kesehatan mental Anda.',
    clinic: 'Klinik ABC Surabaya Utara',
    education: 'Universitas Airlangga, 2015',
    experience: '4 tahun',
    services: [
      'Beng<PERSON>',
      'Konsultasi Psikologi',
      'Op<PERSON> Wae'
    ],
    links: [
      'Home',
      'Layanan',
      'Contact',
      'Login',
      'Cart'
    ]
  };

  const doctorData = {
    ...defaultDoctor,
    ...doctor,
    services: doctor?.services || defaultDoctor.services,
    links: doctor?.links || defaultDoctor.links
  };

  const handleBack = () => {
    navigate(-1);
  };

  const handleBookConsultation = () => {
    navigate('/psychology/consultation', { state: { psychologist: doctorData } });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-8">
              <h1 className="text-xl font-bold text-gray-800">Future X</h1>
              <p className="text-sm text-gray-600">Minibiografi dokter</p>
            </div>
            <nav className="flex items-center space-x-6">
              <a href="#" className="text-gray-600 hover:text-gray-800">Home</a>
              <a href="#" className="text-gray-600 hover:text-gray-800">Layanan</a>
              <a href="#" className="text-gray-600 hover:text-gray-800">Contact</a>
              <div className="flex items-center space-x-2">
                <span className="text-gray-600">🛒</span>
                <span className="text-gray-600">👤</span>
              </div>
            </nav>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <div className="relative bg-gradient-to-r from-blue-50 to-indigo-100 py-16">
        <div className="absolute inset-0 bg-black bg-opacity-20"></div>
        <div className="relative max-w-6xl mx-auto px-4">
          <div className="flex items-center mb-6">
            <button
              onClick={handleBack}
              className="flex items-center text-gray-700 hover:text-gray-900 mr-4"
            >
              <FaArrowLeft className="mr-2" />
              Kembali
            </button>
          </div>
          
          <div className="bg-white bg-opacity-90 backdrop-blur-sm rounded-xl p-8 max-w-2xl">
            <h1 className="text-3xl font-bold text-gray-800 mb-4">
              Konsultasi Psikologi
            </h1>
            <p className="text-gray-600 leading-relaxed">
              {doctorData.bio}
            </p>
          </div>
        </div>
      </div>

      {/* Doctor Profile Section */}
      <div className="py-16">
        <div className="max-w-6xl mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            {/* Doctor Image */}
            <div className="flex justify-center">
              <div className="relative">
                <img
                  src={doctorData.image}
                  alt={doctorData.name}
                  className="w-80 h-96 object-cover rounded-lg shadow-lg"
                />
              </div>
            </div>

            {/* Doctor Information */}
            <div className="space-y-6">
              <div>
                <h2 className="text-3xl font-bold text-gray-800 mb-2">
                  {doctorData.name}
                </h2>
                <p className="text-lg text-gray-600 mb-6">
                  {doctorData.specialization}
                </p>
              </div>

              {/* Practice Location */}
              <div className="bg-gray-100 rounded-lg p-4">
                <h3 className="font-semibold text-gray-800 mb-2">Tempat Praktik</h3>
                <div className="flex items-center text-gray-600">
                  <FaMapMarkerAlt className="mr-2" />
                  <span>{doctorData.clinic}</span>
                </div>
              </div>

              {/* Education */}
              <div className="bg-gray-100 rounded-lg p-4">
                <h3 className="font-semibold text-gray-800 mb-2">Alumni</h3>
                <div className="flex items-center text-gray-600">
                  <FaGraduationCap className="mr-2" />
                  <span>{doctorData.education}</span>
                </div>
              </div>

              {/* Book Consultation Button */}
              <button
                onClick={handleBookConsultation}
                className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-semibold"
              >
                Booking Konsultasi
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-12">
        <div className="max-w-6xl mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-8">
            {/* Company Info */}
            <div>
              <h3 className="text-xl font-bold mb-4">Future X</h3>
              <p className="text-gray-300 text-sm leading-relaxed">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
              </p>
              <div className="flex space-x-4 mt-4">
                <a href="#" className="text-gray-300 hover:text-white">📘</a>
                <a href="#" className="text-gray-300 hover:text-white">📷</a>
                <a href="#" className="text-gray-300 hover:text-white">📺</a>
              </div>
            </div>

            {/* Our Services */}
            <div>
              <h4 className="font-semibold mb-4">Our Services</h4>
              <ul className="space-y-2 text-gray-300 text-sm">
                {(doctorData.services || []).map((service, index) => (
                  <li key={index}>▶ {service}</li>
                ))}
              </ul>
            </div>

            {/* Useful Links */}
            <div>
              <h4 className="font-semibold mb-4">Useful Links</h4>
              <ul className="space-y-2 text-gray-300 text-sm">
                {(doctorData.links || []).map((link, index) => (
                  <li key={index}>▶ {link}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default DoctorBiography;
